package interactor_test

import (
	"context"
	"log"
	"net/http"
	"net/http/httptest"
	"testing"

	"sa-intranet/core"
	corerepository "sa-intranet/core/repository"
	testhelpers "sa-intranet/testing"
	"sa-intranet/usecase/cqm/interactor"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"
	"sa-intranet/usecase/cqm/sonarqube"

	"github.com/google/uuid"
	"github.com/samber/do"
)

var injector *do.Injector

func TestMain(m *testing.M) {
	ctx := context.Background()
	injector = do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx), // Apply migrations, needs to be after WithPostgres
		testhelpers.WithFixtures(ctx),   // Load fixtures, needs to be after WithMigrations
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	// Run all tests
	m.Run()
}

func TestCompanyClientsInteractorInitialData(t *testing.T) {
	inj := injector.Clone()
	usecase := do.MustInvoke[*interactor.CompanyClientsInteractor](inj)

	got, err := usecase.InitialData("", 1, 10)
	if err != nil {
		t.Errorf("InitialData() error = %v, want nil", err)
	}
	if got.Pagination.CurrentPage != 1 {
		t.Errorf("Expected current page 1, got %d", got.Pagination.CurrentPage)
	}
	if got.Pagination.PerPage != 10 {
		t.Errorf("Expected per page 10, got %d", got.Pagination.PerPage)
	}
}

func TestCompanyClientsInteractorCreateCompanyClient(t *testing.T) {
	inj := injector.Clone()
	usecase := do.MustInvoke[*interactor.CompanyClientsInteractor](inj)

	companyClient := interactor.CompanyClientValidator{
		Name: "Test Company",
	}
	got, validationErrors, err := usecase.CreateCompanyClient(companyClient)
	if err != nil {
		t.Errorf("CreateCompanyClient() error = %v, want nil", err)
	}
	if got.Name != companyClient.Name {
		t.Errorf("Expected name %s, got %s", companyClient.Name, got.Name)
	}
	if validationErrors != nil {
		t.Errorf("Expected no validation errors, got %v", validationErrors)
	}
}

func TestCompanyClientsInteractorUpdateCompanyClient(t *testing.T) {
	inj := injector.Clone()
	usecase := do.MustInvoke[*interactor.CompanyClientsInteractor](inj)

	// First create a company client
	createReq := interactor.CompanyClientValidator{
		Name: "Original Company",
	}
	created, _, err := usecase.CreateCompanyClient(createReq)
	if err != nil {
		t.Fatalf("Failed to create test company client: %v", err)
	}

	// Now update it
	updateReq := interactor.CompanyClientValidator{
		Name: "Updated Company",
	}
	updated, validationErrors, err := usecase.UpdateCompanyClient(created.ID.String(), updateReq)
	if err != nil {
		t.Errorf("UpdateCompanyClient() error = %v, want nil", err)
	}
	if updated.Name != updateReq.Name {
		t.Errorf("Expected name %s, got %s", updateReq.Name, updated.Name)
	}
	if validationErrors != nil {
		t.Errorf("Expected no validation errors, got %v", validationErrors)
	}
}

func TestJiraProjectsInteractorInitialData(t *testing.T) {
	inj := injector.Clone()
	usecase := do.MustInvoke[*interactor.JiraProjectsInteractor](inj)

	got, err := usecase.InitialData("", 1, 10)
	if err != nil {
		t.Errorf("InitialData() error = %v, want nil", err)
	}
	if got.Pagination.CurrentPage != 1 {
		t.Errorf("Expected current page 1, got %d", got.Pagination.CurrentPage)
	}
	if got.Pagination.PerPage != 10 {
		t.Errorf("Expected per page 10, got %d", got.Pagination.PerPage)
	}
}

func TestSonarProjectsInteractorInitialData(t *testing.T) {
	inj := injector.Clone()
	usecase := do.MustInvoke[*interactor.SonarProjectsInteractor](inj)

	got, err := usecase.InitialData("", 1, 10)
	if err != nil {
		t.Errorf("InitialData() error = %v, want nil", err)
	}
	if got.Pagination.CurrentPage != 1 {
		t.Errorf("Expected current page 1, got %d", got.Pagination.CurrentPage)
	}
	if got.Pagination.PerPage != 10 {
		t.Errorf("Expected per page 10, got %d", got.Pagination.PerPage)
	}
}

func TestSonarqubeProjectRepositoryList(t *testing.T) {
	inj := injector.Clone()
	repo := do.MustInvoke[repository.SonarqubeProjectRepository](inj)

	// Test the repository List method directly to ensure coverage
	params := corerepository.PaginationParams[repository.SonarqubeProjectFilter]{
		Filters: repository.SonarqubeProjectFilter{
			Name: "",
		},
		Page:     1,
		PageSize: 10,
	}

	result, err := repo.List(context.Background(), params)
	if err != nil {
		t.Errorf("List() error = %v, want nil", err)
	}
	if result == nil {
		t.Error("Expected non-nil result")
	}
	if result.Page != 1 {
		t.Errorf("Expected page 1, got %d", result.Page)
	}
	if result.PageSize != 10 {
		t.Errorf("Expected page size 10, got %d", result.PageSize)
	}

	// Test with filter to ensure the query builder logic is covered
	paramsWithFilter := corerepository.PaginationParams[repository.SonarqubeProjectFilter]{
		Filters: repository.SonarqubeProjectFilter{
			Name:       "test",
			ProjectKey: "TEST_KEY",
		},
		Page:     1,
		PageSize: 5,
	}

	resultWithFilter, err := repo.List(context.Background(), paramsWithFilter)
	if err != nil {
		t.Errorf("List() with filter error = %v, want nil", err)
	}
	if resultWithFilter == nil {
		t.Error("Expected non-nil result with filter")
	}
}

func TestSonarIssuesInteractorGetSonarIssues(t *testing.T) {
	inj := injector.Clone()
	usecase := do.MustInvoke[*interactor.SonarIssuesInteractor](inj)

	// Test that the method exists and doesn't panic
	// We expect this to fail since we don't have a real SonarQube server
	// but it should fail gracefully without panicking
	_, err := usecase.GetSonarIssues("test-project", 1, 10)

	// We expect an error since there's no real SonarQube server
	// The important thing is that it doesn't panic
	if err == nil {
		t.Log("Unexpected success - SonarQube server might be available")
	} else {
		t.Logf("Expected error occurred: %v", err)
	}
}

func setupSonarQubeMockServer() *httptest.Server {
	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		log.Printf("Request path: %s", r.URL.Path)
		jsonDefaultSuccess, err := testhelpers.FixtureFS.ReadFile("testdata/sonarqube/issues-default-success-response.json")
		if err != nil {
			panic(err)
		}
		switch r.URL.Path {
		case "/api/issues/search":
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write(jsonDefaultSuccess)
		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))
}

func setupJiraMockServer() *httptest.Server {
	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		log.Printf("Request path: %s", r.URL.Path)
		jsonDefaultProjectSuccess, err := testhelpers.FixtureFS.ReadFile("testdata/jira/project-default-success-response.json")
		if err != nil {
			panic(err)
		}
		switch r.URL.Path {
		case "/rest/api/3/project/TEST":
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write(jsonDefaultProjectSuccess)
		case "/rest/api/3/issue":
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusCreated)
			w.Write([]byte(`{}`))
		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))
}

func TestSonarIssuesInteractor(t *testing.T) {
	t.Parallel()

	companyClientRepo := do.MustInvoke[repository.CompanyClientRepository](injector)

	jiraRepo := do.MustInvoke[repository.JiraProjectRepository](injector)

	sonarProjectRepo := do.MustInvoke[repository.SonarqubeProjectRepository](injector)

	cipher := do.MustInvoke[core.Cypher](injector)

	t.Run("CreateJiraTicket", func(t *testing.T) {
		t.Parallel()

		mockSonarServer := setupSonarQubeMockServer()
		defer mockSonarServer.Close()

		do.Override(injector, func(i *do.Injector) (sonarqube.ClientConfig, error) {
			config := sonarqube.ClientConfig{
				URL:   mockSonarServer.URL,
				Token: "test-token",
			}

			return config, nil
		})

		usecase := do.MustInvoke[*interactor.SonarIssuesInteractor](injector)

		mockJiraServer := setupJiraMockServer()
		defer mockJiraServer.Close()

		companyClient := model.CompanyClient{
			Name: "Test Company",
		}

		_, err := companyClientRepo.Save(context.TODO(), &companyClient)
		if err != nil {
			t.Fatalf("Failed to save company client: %v", err)
		}

		encrypedToken, err := cipher.Encrypt("W7JC59uDmQVgwecHIB-gQrk-mG4xJ3bycbZi2SOcFH0")
		if err != nil {
			t.Fatalf("Failed to encrypt token: %v", err)
		}

		jiraProject := model.JiraProject{
			Name:            "Test Jira Project",
			ProjectKey:      "TEST",
			CompanyClientID: companyClient.ID,
			JiraURL:         mockJiraServer.URL,
			Username:        "<EMAIL>",
			Token:           encrypedToken,
		}

		_, err = jiraRepo.Save(context.TODO(), &jiraProject)
		if err != nil {
			t.Fatalf("Failed to save Jira project: %v", err)
		}

		projectUUID, err := uuid.Parse("0195dda6-0db4-74a6-8d38-4464b32a8d85")
		if err != nil {
			t.Fatalf("Failed to parse project UUID: %v", err)
		}

		project := model.SonarqubeProject{
			ID:            projectUUID,
			ProjectKey:    "test_project",
			ProjectName:   "Test Project",
			JiraProjectID: jiraProject.ID,
		}

		_, err = sonarProjectRepo.Save(context.TODO(), &project, true)
		if err != nil {
			t.Fatalf("Failed to save SonarQube project: %v", err)
		}

		_, err = usecase.CreateJiraIssue("0195dda6-0db4-74a6-8d38-4464b32a8d85", "AZUqtlaa7J7LX6RjQIpc")
		if err != nil {
			t.Fatalf("CreateJiraTicket failed: %v", err)
		}

		t.Log("CreateJiraTicket passed")
	})
}
